# EduFair API Documentation - Recent Subscription System`

## 🔐 **Authentication**
All protected endpoints require a Bearer token:
```http
Authorization: Bearer <your_jwt_token>
```

---

## 📚 **Subscription System**

### **Get Available Plans**
```http
GET /api/subscriptions/plans
```

**Query Parameters:**
- `user_type` (optional): `student` | `teacher` | `institute` | `mentor`
- `is_active` (optional): `true` | `false` (default: `true`)
- `skip` (optional): Pagination offset (default: `0`)
- `limit` (optional): Items per page (default: `100`)

**Example Request:**
```http
GET /api/subscriptions/plans?user_type=teacher&is_active=true
```

**Response (200):**
```json
[
  {
    "id": "550e8400-e29b-41d4-a716-446655440001",
    "name": "Teacher Premium",
    "description": "Premium plan for teachers with advanced features",
    "price": 2999,
    "duration_days": 30,
    "features": {
      "create_classroom": true,
      "create_exam": true,
      "ai_question_generation": true,
      "advanced_analytics": true,
      "bulk_operations": true,
      "priority_support": true
    },
    "is_active": true,
    "plan_type": "premium",
    "target_user_type": "teacher",
    "max_classrooms": 20,
    "max_students_per_classroom": 100,
    "max_exams_per_month": 50,
    "max_questions_per_exam": 200,
    "allows_home_tutoring": false,
    "allows_ai_question_generation": true,
    "allows_advanced_analytics": true,
    "priority_support": true,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  },
  {
    "id": "550e8400-e29b-41d4-a716-446655440002",
    "name": "Teacher Home Tutor",
    "description": "Special plan for home tutoring services",
    "price": 4999,
    "duration_days": 30,
    "features": {
      "create_classroom": true,
      "create_exam": true,
      "ai_question_generation": true,
      "advanced_analytics": true,
      "home_tutoring": true,
      "bulk_operations": true,
      "priority_support": true,
      "payment_processing": true
    },
    "is_active": true,
    "plan_type": "home_tutor",
    "target_user_type": "teacher",
    "max_classrooms": 50,
    "max_students_per_classroom": 200,
    "max_exams_per_month": 100,
    "max_questions_per_exam": 500,
    "allows_home_tutoring": true,
    "allows_ai_question_generation": true,
    "allows_advanced_analytics": true,
    "priority_support": true,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
]
```

### **Get My Subscription**
```http
GET /api/subscriptions/my-subscription
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Response (200):**
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440010",
  "user_id": "123e4567-e89b-12d3-a456-426614174000",
  "plan_id": "550e8400-e29b-41d4-a716-446655440001",
  "start_date": "2024-01-15T10:30:00Z",
  "end_date": "2024-02-15T10:30:00Z",
  "trial_end_date": null,
  "next_billing_date": "2024-02-15T10:30:00Z",
  "status": "active",
  "auto_renew": true,
  "billing_cycle": "monthly",
  "is_trial": false,
  "payment_reference": "dummy_payment_ref_123",
  "current_usage": {
    "classrooms_created": 5,
    "students_enrolled": 150,
    "exams_created": 12,
    "questions_generated": 45,
    "ai_questions_used": 23,
    "analytics_views": 8
  },
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z",
  "plan": {
    "id": "550e8400-e29b-41d4-a716-446655440001",
    "name": "Teacher Premium",
    "description": "Premium plan for teachers with advanced features",
    "price": 2999,
    "duration_days": 30,
    "plan_type": "premium",
    "target_user_type": "teacher",
    "max_classrooms": 20,
    "max_students_per_classroom": 100,
    "max_exams_per_month": 50,
    "allows_ai_question_generation": true,
    "allows_home_tutoring": false,
    "allows_advanced_analytics": true,
    "priority_support": true,
    "is_active": true,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

**Response (404) - No Subscription:**
```json
null
```

### **Upgrade Subscription**
```http
POST /api/subscriptions/upgrade
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "plan_id": "550e8400-e29b-41d4-a716-446655440002"
}
```

**Response (200):**
```json
{
  "message": "Subscription upgraded successfully",
  "subscription": {
    "id": "550e8400-e29b-41d4-a716-446655440010",
    "user_id": "123e4567-e89b-12d3-a456-426614174000",
    "plan_id": "550e8400-e29b-41d4-a716-446655440002",
    "status": "active",
    "start_date": "2024-01-15T10:30:00Z",
    "end_date": "2024-02-15T10:30:00Z",
    "billing_cycle": "monthly",
    "auto_renew": true,
    "is_trial": false,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

**Error Responses:**
```json
// 404 - Plan not found
{
  "detail": "Subscription plan not found"
}

// 400 - Plan not available for user type
{
  "detail": "Plan is not available for teacher users"
}
```

### **Get My Usage**
```http
GET /api/subscriptions/usage/my-usage
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Response (200):**
```json
{
  "user_id": "123e4567-e89b-12d3-a456-426614174000",
  "plan_name": "Teacher Premium",
  "current_usage": {
    "classrooms_created": 5,
    "students_enrolled": 150,
    "exams_created": 12,
    "questions_generated": 45,
    "ai_questions_used": 23,
    "analytics_views": 8
  },
  "plan_limits": {
    "max_classrooms": 20,
    "max_students_per_classroom": 100,
    "max_exams_per_month": 50,
    "max_questions_per_exam": 200
  },
  "usage_percentage": {
    "max_classrooms": 25.0,
    "max_exams_per_month": 24.0,
    "max_questions_per_exam": 22.5
  },
  "is_over_limit": false,
  "warnings": [
    "Approaching limit for max_classrooms: 5/20 (25.0%)"
  ]
}
```

**Response (404) - No Subscription:**
```json
null
```

---

## 🏠 **Home Tutoring System**

### **Search Home Tutors**
```http
POST /api/subscriptions/home-tutors/search
```

**Request Body:**
```json
{
  "subject_ids": ["550e8400-e29b-41d4-a716-446655440100"],
  "latitude": 40.7128,
  "longitude": -74.0060,
  "radius_km": 10,
  "max_hourly_rate": 50.00,
  "min_rating": 4.0,
  "available_days": ["monday", "wednesday", "friday"]
}
```

**Response (200):**
```json
{
  "tutors": [
    {
      "teacher_id": "123e4567-e89b-12d3-a456-426614174000",
      "username": "john_teacher",
      "full_name": "John Smith",
      "bio": "Experienced mathematics teacher with 10+ years of experience",
      "experience_years": 10,
      "rating": 4.8,
      "hourly_rate_home": 45.00,
      "hourly_rate_online": 35.00,
      "subjects": [
        {
          "subject_id": "550e8400-e29b-41d4-a716-446655440100",
          "subject_name": "Mathematics"
        }
      ],
      "distance_km": 2.5,
      "available_days": ["monday", "wednesday", "friday", "saturday"],
      "preferred_contact_method": "whatsapp",
      "whatsapp_number": "+1234567890"
    }
  ],
  "total": 1,
  "search_radius_km": 10,
  "center_latitude": 40.7128,
  "center_longitude": -74.0060
}
```

### **Get Home Tutors (Query Parameters)**
```http
GET /api/subscriptions/home-tutors
```

**Query Parameters:**
- `subject_ids` (optional): Array of subject UUIDs
- `latitude` (optional): Search center latitude
- `longitude` (optional): Search center longitude
- `radius_km` (optional): Search radius in kilometers (default: 10)
- `max_hourly_rate` (optional): Maximum hourly rate filter
- `min_rating` (optional): Minimum rating filter
- `skip` (optional): Pagination offset (default: 0)
- `limit` (optional): Items per page (default: 20)

**Example Request:**
```http
GET /api/subscriptions/home-tutors?latitude=40.7128&longitude=-74.0060&radius_km=15&max_hourly_rate=60&min_rating=4.0&skip=0&limit=10
```

**Response:** Same as POST search endpoint

---

## 👨‍🏫 **Teacher Profile Management**

### **Update Teacher Profile**
```http
PUT /api/subscriptions/teacher/profile
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "bio": "Experienced mathematics teacher specializing in advanced calculus",
  "experience_years": 12,
  "specialization": "Advanced Mathematics",
  "website": "https://johnsmith-math.com",
  "office_hours": "Monday-Friday 9AM-5PM",
  "offers_home_tutoring": true,
  "home_address": "123 Main St, New York, NY 10001",
  "latitude": 40.7128,
  "longitude": -74.0060,
  "tutoring_radius_km": 15,
  "hourly_rate_home": 50.00,
  "hourly_rate_online": 40.00,
  "preferred_contact_method": "whatsapp",
  "whatsapp_number": "+1234567890",
  "available_days": ["monday", "tuesday", "wednesday", "thursday", "friday"],
  "available_hours": {
    "monday": ["09:00", "17:00"],
    "tuesday": ["09:00", "17:00"],
    "wednesday": ["09:00", "17:00"],
    "thursday": ["09:00", "17:00"],
    "friday": ["09:00", "15:00"]
  },
  "subject_ids": [
    "550e8400-e29b-41d4-a716-446655440100",
    "550e8400-e29b-41d4-a716-446655440101"
  ]
}
```

**Response (200):**
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440200",
  "teacher_id": "123e4567-e89b-12d3-a456-426614174000",
  "bio": "Experienced mathematics teacher specializing in advanced calculus",
  "experience_years": 12,
  "specialization": "Advanced Mathematics",
  "website": "https://johnsmith-math.com",
  "office_hours": "Monday-Friday 9AM-5PM",
  "rating": 4.8,
  "offers_home_tutoring": true,
  "home_address": "123 Main St, New York, NY 10001",
  "latitude": 40.7128,
  "longitude": -74.0060,
  "tutoring_radius_km": 15,
  "hourly_rate_home": 50.00,
  "hourly_rate_online": 40.00,
  "preferred_contact_method": "whatsapp",
  "whatsapp_number": "+1234567890",
  "available_days": ["monday", "tuesday", "wednesday", "thursday", "friday"],
  "available_hours": {
    "monday": ["09:00", "17:00"],
    "tuesday": ["09:00", "17:00"],
    "wednesday": ["09:00", "17:00"],
    "thursday": ["09:00", "17:00"],
    "friday": ["09:00", "15:00"]
  },
  "subjects": [
    {
      "subject_id": "550e8400-e29b-41d4-a716-446655440100",
      "subject_name": "Mathematics"
    },
    {
      "subject_id": "550e8400-e29b-41d4-a716-446655440101",
      "subject_name": "Physics"
    }
  ],
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T11:45:00Z"
}
```

### **Get My Teacher Profile**
```http
GET /api/subscriptions/teacher/profile
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Response (200):** Same as update response

### **Enable Home Tutoring**
```http
POST /api/subscriptions/teacher/enable-home-tutoring
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Response (200):**
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440200",
  "teacher_id": "123e4567-e89b-12d3-a456-426614174000",
  "offers_home_tutoring": true,
  "bio": "Experienced mathematics teacher",
  "experience_years": 12,
  "rating": 4.8,
  "subjects": [
    {
      "subject_id": "550e8400-e29b-41d4-a716-446655440100",
      "subject_name": "Mathematics"
    }
  ],
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T11:45:00Z"
}
```

**Error Responses:**
```json
// 400 - Subscription doesn't allow home tutoring
{
  "detail": "Current subscription plan does not allow home tutoring. Please upgrade to a Home Tutor plan."
}

// 400 - No active subscription
{
  "detail": "No active subscription found"
}
```

---

## 📊 **Subscription Analytics (Admin Only)**

### **Get Subscription Statistics**
```http
GET /api/subscriptions/analytics/statistics
```

**Headers:**
```http
Authorization: Bearer <admin_token>
```

**Response (200):**
```json
{
  "total_subscriptions": 1250,
  "active_subscriptions": 980,
  "trial_subscriptions": 45,
  "expired_subscriptions": 225,
  "revenue_this_month": 29850.00,
  "revenue_total": 245670.00,
  "subscriptions_by_plan": {
    "Teacher Basic": 450,
    "Teacher Premium": 320,
    "Teacher Home Tutor": 85,
    "Student Basic": 395
  },
  "subscriptions_by_user_type": {
    "teacher": 855,
    "student": 395
  }
}
```

---

## ⚠️ **Error Handling**

### **Subscription-Related Errors**

#### **Feature Not Available (403)**
```json
{
  "detail": "AI Question Generation is not available in your Teacher Basic. Please upgrade to one of: Teacher Premium, Teacher Home Tutor"
}
```

#### **Usage Limit Exceeded (403)**
```json
{
  "detail": "You have reached your Classroom limit (2/2). Please upgrade your subscription to continue."
}
```

#### **Subscription Expired (403)**
```json
{
  "detail": "Subscription expired. Please renew your subscription to continue using this feature."
}
```

#### **Plan Not Available (400)**
```json
{
  "detail": "Plan is not available for teacher users"
}
```

---

## � **Usage Tracking**

### **Update Usage Metrics**
```http
POST /api/subscriptions/usage/update
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Query Parameters:**
- `metric_name` (required): Name of the metric to update
- `increment` (optional): Amount to increment (default: 1)

**Example Request:**
```http
POST /api/subscriptions/usage/update?metric_name=classrooms_created&increment=1
```

**Response (200):**
```json
{
  "message": "Usage updated successfully"
}
```

**Error Response (404):**
```json
{
  "detail": "User subscription not found"
}
```

---

## 📋 **Available Metrics for Usage Tracking**

- `classrooms_created`
- `students_enrolled`
- `exams_created`
- `questions_generated`
- `ai_questions_used`
- `analytics_views`
- `competitions_created`

---

## 🎯 **Frontend Integration Notes**

### **Subscription Checks**
- Always check user subscription status before showing premium features
- Display upgrade prompts when users hit limits
- Show usage progress bars for limited features

### **Error Handling**
- Handle 403 errors gracefully with upgrade suggestions
- Show clear error messages for subscription-related issues
- Provide direct links to upgrade pages

### **UI Recommendations**
- Badge indicators for plan types (Basic, Premium, Home Tutor)
- Usage meters for limited features
- Clear feature comparison tables
- Prominent upgrade buttons for limited users

### **Auto-Assignment**
- New users automatically get basic plans
- No manual subscription setup required
- Basic features work immediately after registration
